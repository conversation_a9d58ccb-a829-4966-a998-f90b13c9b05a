DATA:
  MAX_SAMPLE_INTERVAL: 400
  MEAN:
  - 0.485
  - 0.456
  - 0.406
  SEARCH:
    CENTER_JITTER: 3.5
    FACTOR: 4.0
    SCALE_JITTER: 0.5
    SIZE: 384
    NUMBER: 2
  STD:
  - 0.229
  - 0.224
  - 0.225
  TEMPLATE:
    CENTER_JITTER: 0
    FACTOR: 2.0
    SCALE_JITTER: 0
    SIZE: 192
    NUMBER: 5
  TRAIN:
    DATASETS_NAME:
    - LASOT
    - GOT10K_vottrain
    - COCO17
    - TRACKINGNET
    - VASTTRACK
    DATASETS_RATIO:
    - 1
    - 1
    - 1
    - 1
    - 1
    SAMPLE_PER_EPOCH: 60000
MODEL:
  ENCODER:
    TYPE: fastitpnl
    STRIDE: 16
    PRETRAIN_TYPE: '/pretrained/fast_itpn_large_1600e_1k.pt'
    DROP_PATH: 0.1
    POS_TYPE: index # index or interpolate
    TOKEN_TYPE_INDICATE: True
    INTERACTION_INDEXES: [[6, 16], [16, 26], [26, 36],[36,46]]
    GRAD_CKPT: True
  NECK:
    D_MODEL: 768
  DECODER:
    TYPE: CENTER
    NUM_CHANNELS: 256
TRAIN:
  ENCODER_MULTIPLIER: 0.1
  BATCH_SIZE: 16
  EPOCH: 300
  GRAD_CLIP_NORM: 0.1
  LR: 0.0004
  LR_DROP_EPOCH: 240
  NUM_WORKER: 10
  OPTIMIZER: ADAMW
  PRINT_INTERVAL: 50
  SCHEDULER:
    TYPE: step
    DECAY_RATE: 0.1
  WEIGHT_DECAY: 0.0001
TEST:
  EPOCH: 300
  SEARCH_FACTOR: 4.0
  SEARCH_SIZE: 384
  TEMPLATE_FACTOR: 2.0
  TEMPLATE_SIZE: 192
  WINDOW: True
  NUM_TEMPLATES: 5
  UPT:
    LASOT: 0.8
    LASOT_EXTENSION_SUBSET: 0.85
    TRACKINGNET: 0.5
    TNL2K: 0.5
    NFS: 0.85
    UAV: 0.4
    VOT20: 0.6
  UPH:
    LASOT: 0.92
    LASOT_EXTENSION_SUBSET: 0.97
    TRACKINGNET: 0.9
    TNL2K: 0.9
    NFS: 0.75
    UAV: 0.88
    VOT20: 0.92
  INTER:
    LASOT: 25
    LASOT_EXTENSION_SUBSET: 50
    TRACKINGNET: 20
    TNL2K: 20
    NFS: 25
    UAV: 40
    VOT20: 10
  MB:
    LASOT: 500
    LASOT_EXTENSION_SUBSET: 500
    TRACKINGNET: 200
    TNL2K: 500
    NFS: 500
    UAV: 500
    VOT20: 500

DATA:
  MAX_SAMPLE_INTERVAL: 400
  MEAN:
  - 0.485
  - 0.456
  - 0.406
  SEARCH:
    CENTER_JITTER: 3.5
    FACTOR: 4.0
    SCALE_JITTER: 0.5
    SIZE: 224
    NUMBER: 2
  STD:
  - 0.229
  - 0.224
  - 0.225
  TEMPLATE:
    CENTER_JITTER: 0
    FACTOR: 2.0
    SCALE_JITTER: 0
    SIZE: 112
    NUMBER: 5
  TRAIN:
    DATASETS_NAME:
    - LASOT
    - GOT10K_vottrain
    - COCO17
    - TRACKINGNET
    - VASTTRACK
    DATASETS_RATIO:
    - 1
    - 1
    - 1
    - 1
    - 1
    SAMPLE_PER_EPOCH: 60000
MODEL:
  ENCODER:
    TYPE: fastitpnb
    STRIDE: 16
    PRETRAIN_TYPE: '/pretrained/fast_itpn_base_clipl_e1600.pt'
    DROP_PATH: 0.1
    POS_TYPE: index # index or interpolate
    TOKEN_TYPE_INDICATE: True
    INTERACTION_INDEXES: [[8, 14], [14, 20], [20, 26],[26,32]]
    GRAD_CKPT: True
  NECK:
    D_MODEL: 512
  DECODER:
    TYPE: CENTER
    NUM_CHANNELS: 256
TRAIN:
  ENCODER_MULTIPLIER: 0.1
  BATCH_SIZE: 64
  EPOCH: 300
  GRAD_CLIP_NORM: 0.1
  LR: 0.0004
  LR_DROP_EPOCH: 240
  NUM_WORKER: 10
  OPTIMIZER: ADAMW
  PRINT_INTERVAL: 50
  SCHEDULER:
    TYPE: step
    DECAY_RATE: 0.1
  WEIGHT_DECAY: 0.0001
TEST:
  EPOCH: 300
  SEARCH_FACTOR: 4.0
  SEARCH_SIZE: 224
  TEMPLATE_FACTOR: 2.0
  TEMPLATE_SIZE: 112
  WINDOW: True
  NUM_TEMPLATES: 5
  UPT:
    LASOT: 0.8
    LASOT_EXTENSION_SUBSET: 0.85
    TRACKINGNET: 0.5
    TNL2K: 0.5
    NFS: 0.8
    UAV: 0.2
    VOT20: 0.4
  UPH:
    LASOT: 0.88
    LASOT_EXTENSION_SUBSET: 0.97
    TRACKINGNET: 0.9
    TNL2K: 0.9
    NFS: 0.92
    UAV: 0.91
    VOT20: 0.94
  INTER:
    LASOT: 70
    LASOT_EXTENSION_SUBSET: 50
    TRACKINGNET: 20
    TNL2K: 20
    NFS: 90
    UAV: 1
    VOT20: 1
  MB:
    LASOT: 500
    LASOT_EXTENSION_SUBSET: 500
    TRACKINGNET: 200
    TNL2K: 500
    NFS: 500
    UAV: 400
    VOT20: 500

DATA:
  MAX_SAMPLE_INTERVAL: 400
  MEAN:
  - 0.485
  - 0.456
  - 0.406
  SEARCH:
    CENTER_JITTER: 3.5
    FACTOR: 4.0
    SCALE_JITTER: 0.5
    SIZE: 224
    NUMBER: 2
  STD:
  - 0.229
  - 0.224
  - 0.225
  TEMPLATE:
    CENTER_JITTER: 0
    FACTOR: 2.0
    SCALE_JITTER: 0
    SIZE: 112
    NUMBER: 5
  TRAIN:
    DATASETS_NAME:
    - GOT10K_train_full
    DATASETS_RATIO:
    - 1
    SAMPLE_PER_EPOCH: 60000
MODEL:
  ENCODER:
    TYPE: fastitpnt
    STRIDE: 16
    PRETRAIN_TYPE: '/pretrained/fast_itpn_tiny_1600e_1k.pt'
    DROP_PATH: 0.1
    POS_TYPE: index # index or interpolate
    TOKEN_TYPE_INDICATE: True
    INTERACTION_INDEXES: [[4, 7], [7, 10], [10, 13],[13, 16]]
    GRAD_CKPT: False
  NECK:
    D_MODEL: 384
  DECODER:
    TYPE: CENTER
    NUM_CHANNELS: 256
TRAIN:
  ENCODER_MULTIPLIER: 0.1
  BATCH_SIZE: 64
  EPOCH: 100
  GRAD_CLIP_NORM: 0.1
  LR: 0.0004
  LR_DROP_EPOCH: 80
  NUM_WORKER: 10
  OPTIMIZER: ADAMW
  PRINT_INTERVAL: 50
  SCHEDULER:
    TYPE: step
    DECAY_RATE: 0.1
  WEIGHT_DECAY: 0.0001
TEST:
  EPOCH: 100
  SEARCH_FACTOR: 4.0
  SEARCH_SIZE: 224
  TEMPLATE_FACTOR: 2.0
  TEMPLATE_SIZE: 112
  WINDOW: True
  NUM_TEMPLATES: 5
  UPT:
    GOT10K_TEST: 0.5
  UPH:
    GOT10K_TEST: 0.88
  INTER:
    GOT10K_TEST: 1
  MB:
    GOT10K_TEST: 100